package com.zto.devops.qc.client.service.token.model;

import lombok.Data;

import java.io.Serializable;

/**
 * Token信息响应
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
public class TokenInfoResp implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 过期时间戳
     */
    private Long expireTime;
    
    /**
     * 是否已过期
     */
    private Boolean expired;
    
    /**
     * 是否即将过期（剩余时间少于1分钟）
     */
    private Boolean expiringSoon;
    
    /**
     * 剩余有效时间（秒）
     */
    private Long remainingSeconds;
}
