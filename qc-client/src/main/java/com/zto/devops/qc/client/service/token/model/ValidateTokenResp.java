package com.zto.devops.qc.client.service.token.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 验证Token响应
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
public class ValidateTokenResp implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 是否有效
     */
    private Boolean valid;
    
    /**
     * 验证消息
     */
    private String message;
    
    /**
     * 是否已过期
     */
    private Boolean expired;
    
    /**
     * 过期时间戳
     */
    private Long expireTime;
    
    /**
     * 剩余有效时间（秒）
     */
    private Long remainingSeconds;
}
