package com.zto.devops.qc.client.service.token.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 生成Token响应
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
public class GenerateTokenResp implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 生成的token
     */
    private String token;
    
    /**
     * 过期时间戳
     */
    private Long expireTime;
    
    /**
     * 剩余有效时间（秒）
     */
    private Long remainingSeconds;
    
    /**
     * 使用说明
     */
    private String usage = "请在请求头中添加 'X-QC-Token: {token}' 或在请求参数中添加 'token={token}'";
}
