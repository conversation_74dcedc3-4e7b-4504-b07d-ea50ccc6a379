package com.zto.devops.qc.client.service.token.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 撤销Token请求
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
public class RevokeTokenReq implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 要撤销的token
     */
    private String token;
    
    /**
     * 撤销原因（可选）
     */
    private String reason;
}
