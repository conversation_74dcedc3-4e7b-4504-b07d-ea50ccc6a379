package com.zto.devops.qc.client.constants;

/**
 * Token相关常量定义
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
public class TokenConstant {
    
    /**
     * Token配置相关常量
     */
    public static final String TOKEN_SECRET_KEY_CONFIG = "qc.token.secret-key";
    public static final String TOKEN_EXPIRE_MINUTES_CONFIG = "qc.token.expire-minutes";
    public static final String TOKEN_ENABLE_REPLAY_PROTECTION_CONFIG = "qc.token.enable-replay-protection";
    
    /**
     * 默认配置值
     */
    public static final String DEFAULT_SECRET_KEY = "qc-devops-token-secret-2024";
    public static final int DEFAULT_EXPIRE_MINUTES = 5;
    public static final boolean DEFAULT_ENABLE_REPLAY_PROTECTION = true;
    
    /**
     * Redis缓存key前缀
     */
    public static final String TOKEN_CACHE_PREFIX = "qc:token:";
    public static final String TOKEN_BLACKLIST_PREFIX = "qc:token:blacklist:";
    public static final String TOKEN_USER_PREFIX = "qc:token:user:";
    
    /**
     * Token参数名称
     */
    public static final String TOKEN_HEADER_NAME = "X-QC-Token";
    public static final String TOKEN_PARAM_NAME = "token";
    public static final String AUTHORIZATION_HEADER = "Authorization";
    public static final String BEARER_PREFIX = "Bearer ";
    
    /**
     * 错误消息
     */
    public static final String ERROR_TOKEN_EMPTY = "Token不能为空";
    public static final String ERROR_TOKEN_INVALID = "Token无效";
    public static final String ERROR_TOKEN_EXPIRED = "Token已过期";
    public static final String ERROR_TOKEN_REVOKED = "Token已被撤销";
    public static final String ERROR_TOKEN_USED = "Token已被使用";
    public static final String ERROR_USER_NOT_FOUND = "用户信息不存在";
    public static final String ERROR_AUTH_FAILED = "鉴权失败";
    
    /**
     * 时间相关常量
     */
    public static final long MINUTE_IN_MILLIS = 60 * 1000L;
    public static final long HOUR_IN_MILLIS = 60 * MINUTE_IN_MILLIS;
    public static final long DAY_IN_MILLIS = 24 * HOUR_IN_MILLIS;
    
    /**
     * Token状态
     */
    public enum TokenStatus {
        VALID("有效"),
        EXPIRED("已过期"),
        INVALID("无效"),
        REVOKED("已撤销"),
        USED("已使用");
        
        private final String description;
        
        TokenStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * Token类型
     */
    public enum TokenType {
        ACCESS("访问令牌"),
        REFRESH("刷新令牌"),
        API("API令牌");
        
        private final String description;
        
        TokenType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
