package com.zto.devops.qc.client.service.token;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.service.token.model.*;

/**
 * Token管理服务接口
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
public interface TokenManagementService {
    
    /**
     * 生成访问token
     * 
     * @param req 生成token请求
     * @return token信息
     */
    Result<GenerateTokenResp> generateToken(GenerateTokenReq req);
    
    /**
     * 验证token
     * 
     * @param req 验证token请求
     * @return 验证结果
     */
    Result<ValidateTokenResp> validateToken(ValidateTokenReq req);
    
    /**
     * 刷新token
     * 
     * @param req 刷新token请求
     * @return 新token信息
     */
    Result<RefreshTokenResp> refreshToken(RefreshTokenReq req);
    
    /**
     * 撤销token
     * 
     * @param req 撤销token请求
     * @return 操作结果
     */
    Result<Void> revokeToken(RevokeTokenReq req);
    
    /**
     * 获取token信息
     * 
     * @param req 获取token信息请求
     * @return token信息
     */
    Result<TokenInfoResp> getTokenInfo(TokenInfoReq req);
}
