package com.zto.devops.qc.client.service.token.model;


import java.io.Serializable;

/**
 * 生成Token请求
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
public class GenerateTokenReq implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 产品编码
     */
    private String productCode;
    
    /**
     * 自定义过期时间（分钟）
     * 如果不指定，使用系统默认值
     */
    private Integer expireMinutes;
    
    /**
     * Token用途描述
     */
    private String purpose;
}
